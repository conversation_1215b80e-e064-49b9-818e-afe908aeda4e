using System.ComponentModel;
using System.Text;
using ModelContextProtocol.Server;

namespace HotPreview.Tooling.McpServer.Tools.FileSystem;

[McpServerToolType]
public class FileSystemTools
{
    /// <summary>
    /// Reads a file from the specified path and returns its content.
    /// </summary>
    /// <param name="filePath">The path to the file to read.</param>
    /// <returns>The content of the file as a string.</returns>
    [McpServerTool(Name = "read_file")]
    [Description("Reads a file from the specified path on user system")]
    public string ReadFile(string filePath)
    {
        try
        {
            if (string.IsNullOrEmpty(filePath))
            {
                throw new ArgumentNullException(nameof(filePath), "File path cannot be null or empty");
            }

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"File not found: {filePath}");
            }

            return File.ReadAllText(filePath);
        }
        catch (Exception ex)
        {
            return $"Error reading file: {ex.Message}";
        }
    }

    /// <summary>
    /// Writes text content to a file at the specified path.
    /// </summary>
    /// <param name="filePath">The path where the file will be written.</param>
    /// <param name="content">The content to write to the file.</param>
    /// <returns>A success or error message.</returns>
    [McpServerTool(Name = "write_file")]
    [Description("Writes text content to a file at the specified path on user system")]
    public string WriteFile(string filePath, string content)
    {
        try
        {
            if (string.IsNullOrEmpty(filePath))
            {
                throw new ArgumentNullException(nameof(filePath), "File path cannot be null or empty");
            }

            // Create directory if it doesn't exist
            string? directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            File.WriteAllText(filePath, content);
            return $"File written successfully to: {filePath}";
        }
        catch (Exception ex)
        {
            return $"Error writing file: {ex.Message}";
        }
    }

    /// <summary>
    /// Lists files and directories at the specified path.
    /// </summary>
    /// <param name="directoryPath">The path to the directory to list.</param>
    /// <returns>A formatted string listing the contents of the directory.</returns>
    [McpServerTool(Name = "list_directory")]
    [Description("Lists files and directories at the specified path on user system.")]
    public string ListDirectory(string directoryPath)
    {
        try
        {
            if (string.IsNullOrEmpty(directoryPath))
            {
                throw new ArgumentNullException(nameof(directoryPath), "Directory path cannot be null or empty");
            }

            if (!Directory.Exists(directoryPath))
            {
                throw new DirectoryNotFoundException($"Directory not found: {directoryPath}");
            }

            var result = new StringBuilder();
            result.AppendLine($"Contents of directory: {directoryPath}");
            result.AppendLine();

            // List directories first
            var directories = Directory.GetDirectories(directoryPath);
            foreach (var dir in directories)
            {
                result.AppendLine($"[DIR]  {Path.GetFileName(dir)}");
            }

            // Then list files
            var files = Directory.GetFiles(directoryPath);
            foreach (var file in files)
            {
                var fileInfo = new FileInfo(file);
                result.AppendLine($"[FILE] {Path.GetFileName(file)} ({fileInfo.Length} bytes)");
            }

            return result.ToString();
        }
        catch (Exception ex)
        {
            return $"Error listing directory: {ex.Message}";
        }
    }

    /// <summary>
    /// Reads specific lines from a text file.
    /// </summary>
    /// <param name="filePath">The path to the file to read.</param>
    /// <param name="startLine">The starting line number (1-based).</param>
    /// <param name="endLine">The ending line number (1-based).</param>
    /// <returns>The specified lines from the file.</returns>
    [McpServerTool(Name = "read_file_lines")]
    [Description("Reads specific lines from a text file on user system.")]
    public string ReadFileLines(string filePath, int startLine, int endLine)
    {
        try
        {
            if (string.IsNullOrEmpty(filePath))
            {
                throw new ArgumentNullException(nameof(filePath), "File path cannot be null or empty");
            }

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"File not found: {filePath}");
            }

            if (startLine < 1)
            {
                throw new ArgumentException("Start line must be 1 or greater", nameof(startLine));
            }

            if (endLine < startLine)
            {
                throw new ArgumentException("End line must be greater than or equal to start line", nameof(endLine));
            }

            var lines = File.ReadAllLines(filePath);
            var result = new StringBuilder();

            // Convert to 0-based indexing
            int startIndex = startLine - 1;
            int endIndex = Math.Min(endLine - 1, lines.Length - 1);

            var selectedLines = new List<string>();
            for (int i = startIndex; i <= endIndex && i < lines.Length; i++)
            {
                selectedLines.Add(lines[i]);
            }

            return string.Join("\n", selectedLines);
        }
        catch (Exception ex)
        {
            return $"Error reading file lines: {ex.Message}";
        }
    }

    /// <summary>
    /// Writes content to specific lines in a text file, replacing existing lines.
    /// </summary>
    /// <param name="filePath">The path to the file to modify.</param>
    /// <param name="content">The content to write.</param>
    /// <param name="startLine">The starting line number (1-based) where content will be written.</param>
    /// <returns>A success or error message.</returns>
    [McpServerTool(Name = "write_file_lines")]
    [Description("Writes content to specific lines in a text file, replacing existing lines on user system.")]
    public string WriteFileLines(string filePath, string content, int startLine)
    {
        try
        {
            if (string.IsNullOrEmpty(filePath))
            {
                throw new ArgumentNullException(nameof(filePath), "File path cannot be null or empty");
            }

            if (startLine < 1)
            {
                throw new ArgumentException("Start line must be 1 or greater", nameof(startLine));
            }

            var lines = File.Exists(filePath) ? File.ReadAllLines(filePath).ToList() : new List<string>();
            var newLines = content.Split('\n');

            // Convert to 0-based indexing
            int startIndex = startLine - 1;

            // Ensure the list is large enough
            while (lines.Count <= startIndex + newLines.Length - 1)
            {
                lines.Add(string.Empty);
            }

            // Replace lines
            for (int i = 0; i < newLines.Length; i++)
            {
                lines[startIndex + i] = newLines[i].TrimEnd('\r');
            }

            File.WriteAllLines(filePath, lines);
            return $"Lines written successfully to: {filePath}";
        }
        catch (Exception ex)
        {
            return $"Error writing file lines: {ex.Message}";
        }
    }

    /// <summary>
    /// Inserts content at a specific line in a text file, pushing existing content down.
    /// </summary>
    /// <param name="filePath">The path to the file to modify.</param>
    /// <param name="content">The content to insert.</param>
    /// <param name="atLine">The line number (1-based) where content will be inserted.</param>
    /// <returns>A success or error message.</returns>
    [McpServerTool(Name = "insert_file_lines")]
    [Description("Inserts content at a specific line in a text file, pushing existing content down on user system.")]
    public string InsertFileLines(string filePath, string content, int atLine)
    {
        try
        {
            if (string.IsNullOrEmpty(filePath))
            {
                throw new ArgumentNullException(nameof(filePath), "File path cannot be null or empty");
            }

            if (atLine < 1)
            {
                throw new ArgumentException("Line number must be 1 or greater", nameof(atLine));
            }

            var lines = File.Exists(filePath) ? File.ReadAllLines(filePath).ToList() : new List<string>();
            var newLines = content.Split('\n');

            // Convert to 0-based indexing
            int insertIndex = atLine - 1;

            // Ensure the list is large enough
            while (lines.Count < insertIndex)
            {
                lines.Add(string.Empty);
            }

            // Insert lines
            for (int i = newLines.Length - 1; i >= 0; i--)
            {
                lines.Insert(insertIndex, newLines[i].TrimEnd('\r'));
            }

            File.WriteAllLines(filePath, lines);
            return $"Lines inserted successfully into: {filePath}";
        }
        catch (Exception ex)
        {
            return $"Error inserting file lines: {ex.Message}";
        }
    }
}
